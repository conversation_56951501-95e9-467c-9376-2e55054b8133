-- Calendar Integration Migration
-- This migration adds tables for Microsoft Outlook calendar integration

-- Calendar connections table to store OAuth tokens and connection info
CREATE TABLE IF NOT EXISTS calendar_connections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL, -- Clerk user ID
  microsoft_tenant_id TEXT,
  access_token_encrypted TEXT,
  refresh_token_encrypted TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  scopes TEXT[],
  is_active BOOLEAN DEFAULT true,
  last_sync_at TIMESTAMP WITH TIME ZONE,
  sync_status TEXT DEFAULT 'pending', -- pending, syncing, completed, error
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Calendar events table to store synchronized calendar events
CREATE TABLE IF NOT EXISTS calendar_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  microsoft_event_id TEXT UNIQUE NOT NULL,
  connection_id UUID REFERENCES calendar_connections(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
  subject TEXT,
  body_content TEXT,
  body_content_type TEXT DEFAULT 'text', -- text, html
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  location TEXT,
  attendees JSONB DEFAULT '[]'::jsonb,
  organizer JSONB,
  is_all_day BOOLEAN DEFAULT false,
  is_cancelled BOOLEAN DEFAULT false,
  importance TEXT DEFAULT 'normal', -- low, normal, high
  sensitivity TEXT DEFAULT 'normal', -- normal, personal, private, confidential
  show_as TEXT DEFAULT 'busy', -- free, tentative, busy, oof, workingElsewhere, unknown
  
  -- AI Analysis fields
  ai_analysis JSONB DEFAULT '{}'::jsonb,
  confidence_score DECIMAL(3,2) DEFAULT 0.0,
  event_type TEXT, -- sales_meeting, support_call, installation, consultation, follow_up
  priority INTEGER DEFAULT 3, -- 1-5 scale
  extracted_customer_info JSONB DEFAULT '{}'::jsonb,
  sentiment_score DECIMAL(3,2), -- -1.0 to 1.0
  action_items TEXT[],
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_modified TIMESTAMP WITH TIME ZONE, -- From Microsoft Graph
  change_key TEXT -- Microsoft Graph change tracking
);

-- Calendar sync logs for monitoring and debugging
CREATE TABLE IF NOT EXISTS calendar_sync_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  connection_id UUID REFERENCES calendar_connections(id) ON DELETE CASCADE,
  sync_type TEXT NOT NULL, -- full, incremental, webhook
  status TEXT NOT NULL, -- started, completed, failed
  events_processed INTEGER DEFAULT 0,
  events_created INTEGER DEFAULT 0,
  events_updated INTEGER DEFAULT 0,
  events_deleted INTEGER DEFAULT 0,
  error_message TEXT,
  duration_ms INTEGER,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Customer calendar insights for analytics
CREATE TABLE IF NOT EXISTS customer_calendar_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  total_meetings INTEGER DEFAULT 0,
  last_meeting_date TIMESTAMP WITH TIME ZONE,
  next_meeting_date TIMESTAMP WITH TIME ZONE,
  meeting_frequency_days DECIMAL(5,2), -- Average days between meetings
  preferred_meeting_times JSONB DEFAULT '{}'::jsonb,
  common_meeting_types TEXT[],
  engagement_score DECIMAL(3,2) DEFAULT 0.0, -- 0.0 to 1.0
  last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(customer_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_calendar_connections_user_id ON calendar_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_calendar_connections_active ON calendar_connections(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_calendar_events_connection_id ON calendar_events(connection_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_customer_id ON calendar_events(customer_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX IF NOT EXISTS idx_calendar_events_microsoft_id ON calendar_events(microsoft_event_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_event_type ON calendar_events(event_type);
CREATE INDEX IF NOT EXISTS idx_calendar_sync_logs_connection_id ON calendar_sync_logs(connection_id);
CREATE INDEX IF NOT EXISTS idx_calendar_sync_logs_started_at ON calendar_sync_logs(started_at);
CREATE INDEX IF NOT EXISTS idx_customer_calendar_insights_customer_id ON customer_calendar_insights(customer_id);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_calendar_connections_updated_at 
    BEFORE UPDATE ON calendar_connections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_calendar_events_updated_at 
    BEFORE UPDATE ON calendar_events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_calendar_insights_updated_at 
    BEFORE UPDATE ON customer_calendar_insights 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE calendar_connections IS 'Stores Microsoft Graph OAuth connections for calendar access';
COMMENT ON TABLE calendar_events IS 'Synchronized calendar events with AI analysis and customer matching';
COMMENT ON TABLE calendar_sync_logs IS 'Audit log for calendar synchronization operations';
COMMENT ON TABLE customer_calendar_insights IS 'Aggregated calendar insights per customer for analytics';

COMMENT ON COLUMN calendar_events.ai_analysis IS 'JSON object containing AI analysis results including extracted entities, topics, and insights';
COMMENT ON COLUMN calendar_events.confidence_score IS 'AI confidence score for customer matching and analysis (0.0 to 1.0)';
COMMENT ON COLUMN calendar_events.extracted_customer_info IS 'JSON object with extracted customer information from event content';
COMMENT ON COLUMN calendar_events.sentiment_score IS 'Sentiment analysis score from -1.0 (negative) to 1.0 (positive)';
