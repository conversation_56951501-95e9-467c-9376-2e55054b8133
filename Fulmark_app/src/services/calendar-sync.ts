import { eq, and, desc } from 'drizzle-orm';
import { db } from '../libs/db';
import {
  calendarConnections,
  calendarEvents,
  calendarSyncLogs,
  customerCalendarInsights,
  type CalendarConnection,
  type CalendarEvent,
  type NewCalendarEvent,
  type NewCalendarSyncLog,
  SyncStatus,
  SyncType,
  LogStatus,
} from '../models/calendar';
import { microsoftGraphService, type CalendarEventData } from './microsoft-graph';
import { calendarAIAnalysisService } from './calendar-ai-analysis';
import { tokenEncryptionService } from './token-encryption';

export class CalendarSyncService {
  /**
   * Sync calendar events for a specific connection
   */
  async syncCalendarEvents(connectionId: string, syncType: 'full' | 'incremental' = 'incremental'): Promise<void> {
    const startTime = Date.now();
    let syncLog: NewCalendarSyncLog;

    try {
      // Get the calendar connection
      const connection = await db
        .select()
        .from(calendarConnections)
        .where(eq(calendarConnections.id, connectionId))
        .limit(1);

      if (!connection.length || !connection[0].isActive) {
        throw new Error('Calendar connection not found or inactive');
      }

      const conn = connection[0];

      // Create sync log
      syncLog = {
        connectionId,
        syncType: syncType === 'full' ? SyncType.FULL : SyncType.INCREMENTAL,
        status: LogStatus.STARTED,
        eventsProcessed: 0,
        eventsCreated: 0,
        eventsUpdated: 0,
        eventsDeleted: 0,
      };

      const [logResult] = await db.insert(calendarSyncLogs).values(syncLog).returning();
      const logId = logResult.id;

      // Update connection status
      await db
        .update(calendarConnections)
        .set({ syncStatus: SyncStatus.SYNCING })
        .where(eq(calendarConnections.id, connectionId));

      // Decrypt access token
      const accessToken = await tokenEncryptionService.decrypt(conn.accessTokenEncrypted!);

      // Check if token needs refresh
      const now = new Date();
      if (conn.expiresAt && conn.expiresAt <= now) {
        await this.refreshConnectionToken(connectionId);
        // Re-fetch connection with new token
        const refreshedConnection = await db
          .select()
          .from(calendarConnections)
          .where(eq(calendarConnections.id, connectionId))
          .limit(1);
        
        if (!refreshedConnection.length) {
          throw new Error('Failed to refresh connection token');
        }
      }

      // Fetch calendar events
      let events: CalendarEventData[] = [];
      
      if (syncType === 'full') {
        // Full sync: get events from last 6 months to next 6 months
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 6);
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 6);
        
        events = await microsoftGraphService.getCalendarEvents(accessToken, startDate, endDate, 1000);
      } else {
        // Incremental sync: get events from last sync or last 30 days
        const lastSyncDate = conn.lastSyncAt || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const futureDate = new Date();
        futureDate.setMonth(futureDate.getMonth() + 3);
        
        events = await microsoftGraphService.getCalendarEvents(accessToken, lastSyncDate, futureDate, 500);
      }

      // Process events
      const processResults = await this.processCalendarEvents(events, connectionId);

      // Update sync log
      const duration = Date.now() - startTime;
      await db
        .update(calendarSyncLogs)
        .set({
          status: LogStatus.COMPLETED,
          eventsProcessed: events.length,
          eventsCreated: processResults.created,
          eventsUpdated: processResults.updated,
          eventsDeleted: processResults.deleted,
          durationMs: duration,
          completedAt: new Date(),
        })
        .where(eq(calendarSyncLogs.id, logId));

      // Update connection status
      await db
        .update(calendarConnections)
        .set({
          syncStatus: SyncStatus.COMPLETED,
          lastSyncAt: new Date(),
          errorMessage: null,
        })
        .where(eq(calendarConnections.id, connectionId));

      console.log(`Calendar sync completed for connection ${connectionId}: ${processResults.created} created, ${processResults.updated} updated, ${processResults.deleted} deleted`);

    } catch (error) {
      console.error('Calendar sync error:', error);

      // Update sync log with error
      if (syncLog) {
        const duration = Date.now() - startTime;
        await db
          .update(calendarSyncLogs)
          .set({
            status: LogStatus.FAILED,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            durationMs: duration,
            completedAt: new Date(),
          })
          .where(eq(calendarSyncLogs.connectionId, connectionId));
      }

      // Update connection status
      await db
        .update(calendarConnections)
        .set({
          syncStatus: SyncStatus.ERROR,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        })
        .where(eq(calendarConnections.id, connectionId));

      throw error;
    }
  }

  /**
   * Process calendar events: analyze, match customers, and store
   */
  private async processCalendarEvents(
    events: CalendarEventData[],
    connectionId: string
  ): Promise<{ created: number; updated: number; deleted: number }> {
    let created = 0;
    let updated = 0;
    let deleted = 0;

    for (const event of events) {
      try {
        // Check if event already exists
        const existingEvent = await db
          .select()
          .from(calendarEvents)
          .where(eq(calendarEvents.microsoftEventId, event.id))
          .limit(1);

        // Analyze event with AI
        const analysis = await calendarAIAnalysisService.analyzeCalendarEvent(event);

        // Try to match with existing customer
        const customerId = await this.matchCustomer(analysis.extractedCustomerInfo);

        // Prepare event data
        const eventData: Partial<NewCalendarEvent> = {
          microsoftEventId: event.id,
          connectionId,
          customerId,
          subject: event.subject,
          bodyContent: event.body?.content,
          bodyContentType: event.body?.contentType || 'text',
          startTime: event.start?.dateTime ? new Date(event.start.dateTime) : null,
          endTime: event.end?.dateTime ? new Date(event.end.dateTime) : null,
          location: event.location?.displayName,
          attendees: event.attendees || [],
          organizer: event.organizer || {},
          isAllDay: event.isAllDay || false,
          isCancelled: event.isCancelled || false,
          importance: event.importance || 'normal',
          sensitivity: event.sensitivity || 'normal',
          showAs: event.showAs || 'busy',
          aiAnalysis: analysis,
          confidenceScore: analysis.confidenceScore.toString(),
          eventType: analysis.eventType,
          priority: analysis.priority,
          extractedCustomerInfo: analysis.extractedCustomerInfo,
          sentimentScore: analysis.sentimentScore.toString(),
          actionItems: analysis.actionItems,
          lastModified: event.lastModifiedDateTime ? new Date(event.lastModifiedDateTime) : null,
          changeKey: event.changeKey,
        };

        if (existingEvent.length > 0) {
          // Update existing event
          await db
            .update(calendarEvents)
            .set(eventData)
            .where(eq(calendarEvents.id, existingEvent[0].id));
          updated++;
        } else {
          // Create new event
          await db.insert(calendarEvents).values(eventData as NewCalendarEvent);
          created++;
        }

        // Update customer insights if customer matched
        if (customerId) {
          await this.updateCustomerInsights(customerId);
        }

      } catch (error) {
        console.error(`Error processing event ${event.id}:`, error);
        // Continue processing other events
      }
    }

    return { created, updated, deleted };
  }

  /**
   * Match calendar event with existing customer
   */
  private async matchCustomer(extractedInfo: any): Promise<string | null> {
    // This is a simplified customer matching logic
    // In a real implementation, you would query your customers table
    // and use fuzzy matching algorithms
    
    if (!extractedInfo.emailAddresses?.length && !extractedInfo.customerName) {
      return null;
    }

    // TODO: Implement customer matching logic
    // 1. Search by email addresses
    // 2. Search by customer name
    // 3. Search by company name
    // 4. Use fuzzy matching for partial matches
    
    return null; // Placeholder
  }

  /**
   * Update customer calendar insights
   */
  private async updateCustomerInsights(customerId: string): Promise<void> {
    // TODO: Implement customer insights calculation
    // 1. Count total meetings
    // 2. Calculate meeting frequency
    // 3. Identify preferred meeting times
    // 4. Calculate engagement score
    // 5. Update or create insights record
  }

  /**
   * Refresh connection token
   */
  private async refreshConnectionToken(connectionId: string): Promise<void> {
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.id, connectionId))
      .limit(1);

    if (!connection.length || !connection[0].refreshTokenEncrypted) {
      throw new Error('Cannot refresh token: connection or refresh token not found');
    }

    const refreshToken = await tokenEncryptionService.decrypt(connection[0].refreshTokenEncrypted);
    const tokenData = await microsoftGraphService.refreshToken(refreshToken);

    // Encrypt new tokens
    const encryptedAccessToken = await tokenEncryptionService.encrypt(tokenData.accessToken);
    const encryptedRefreshToken = tokenData.refreshToken 
      ? await tokenEncryptionService.encrypt(tokenData.refreshToken)
      : connection[0].refreshTokenEncrypted;

    // Update connection with new tokens
    await db
      .update(calendarConnections)
      .set({
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        expiresAt: tokenData.expiresOn || null,
      })
      .where(eq(calendarConnections.id, connectionId));
  }

  /**
   * Sync all active connections
   */
  async syncAllConnections(): Promise<void> {
    const activeConnections = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.isActive, true));

    const syncPromises = activeConnections.map(connection =>
      this.syncCalendarEvents(connection.id, 'incremental').catch(error => {
        console.error(`Failed to sync connection ${connection.id}:`, error);
      })
    );

    await Promise.all(syncPromises);
  }

  /**
   * Get sync status for a connection
   */
  async getSyncStatus(connectionId: string) {
    const connection = await db
      .select()
      .from(calendarConnections)
      .where(eq(calendarConnections.id, connectionId))
      .limit(1);

    if (!connection.length) {
      throw new Error('Connection not found');
    }

    const recentLogs = await db
      .select()
      .from(calendarSyncLogs)
      .where(eq(calendarSyncLogs.connectionId, connectionId))
      .orderBy(desc(calendarSyncLogs.startedAt))
      .limit(5);

    return {
      connection: connection[0],
      recentLogs,
    };
  }
}

// Singleton instance
export const calendarSyncService = new CalendarSyncService();
