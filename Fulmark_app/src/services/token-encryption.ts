import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For GCM, this is always 16
const SALT_LENGTH = 64;
const TAG_LENGTH = 16;
const ITERATIONS = 100000;

export class TokenEncryptionService {
  private getEncryptionKey(): string {
    const key = process.env.CALENDAR_ENCRYPTION_KEY;
    if (!key) {
      throw new Error('CALENDAR_ENCRYPTION_KEY environment variable is required');
    }
    if (key.length < 32) {
      throw new Error('CALENDAR_ENCRYPTION_KEY must be at least 32 characters long');
    }
    return key;
  }

  /**
   * Encrypt a token using AES-256-GCM
   */
  async encrypt(text: string): Promise<string> {
    try {
      const masterKey = this.getEncryptionKey();
      
      // Generate random salt and IV
      const salt = crypto.randomBytes(SALT_LENGTH);
      const iv = crypto.randomBytes(IV_LENGTH);
      
      // Derive key from master key and salt
      const key = crypto.pbkdf2Sync(masterKey, salt, ITERATIONS, 32, 'sha512');
      
      // Create cipher
      const cipher = crypto.createCipherGCM(ALGORITHM, key, iv);
      
      // Encrypt the text
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Get the authentication tag
      const tag = cipher.getAuthTag();
      
      // Combine salt, iv, tag, and encrypted data
      const combined = Buffer.concat([
        salt,
        iv,
        tag,
        Buffer.from(encrypted, 'hex')
      ]);
      
      // Return base64 encoded result
      return combined.toString('base64');
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt token');
    }
  }

  /**
   * Decrypt a token using AES-256-GCM
   */
  async decrypt(encryptedData: string): Promise<string> {
    try {
      const masterKey = this.getEncryptionKey();
      
      // Decode from base64
      const combined = Buffer.from(encryptedData, 'base64');
      
      // Extract components
      const salt = combined.subarray(0, SALT_LENGTH);
      const iv = combined.subarray(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
      const tag = combined.subarray(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
      const encrypted = combined.subarray(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
      
      // Derive key from master key and salt
      const key = crypto.pbkdf2Sync(masterKey, salt, ITERATIONS, 32, 'sha512');
      
      // Create decipher
      const decipher = crypto.createDecipherGCM(ALGORITHM, key, iv);
      decipher.setAuthTag(tag);
      
      // Decrypt the data
      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt token');
    }
  }

  /**
   * Generate a secure random encryption key
   */
  generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate encryption key format
   */
  validateEncryptionKey(key: string): boolean {
    return typeof key === 'string' && key.length >= 32;
  }

  /**
   * Test encryption/decryption with a sample text
   */
  async testEncryption(): Promise<boolean> {
    try {
      const testText = 'test-token-' + Date.now();
      const encrypted = await this.encrypt(testText);
      const decrypted = await this.decrypt(encrypted);
      return testText === decrypted;
    } catch (error) {
      console.error('Encryption test failed:', error);
      return false;
    }
  }
}

// Singleton instance
export const tokenEncryptionService = new TokenEncryptionService();
