// 📱 SMS Service - PEŁNA MOC WIATRU! ⚡
// AI-powered SMS communication with Twilio integration

import twilio from 'twilio';
import { communicationService } from './communication.service';
import { customerService } from './customer.service';

// Initialize Twilio client
const twilioClient = (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) 
  ? twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)
  : null;

export interface SendSMSData {
  to: string;
  message: string;
  companyId: number;
  customerId?: number;
  serviceTicketId?: number;
  templateType?: 'appointment_reminder' | 'service_update' | 'emergency_response' | 'follow_up' | 'quote_ready';
  templateData?: Record<string, any>;
}

export interface InboundSMSData {
  from: string;
  to: string;
  body: string;
  messageId: string;
  companyId: number;
}

export interface SMSTemplate {
  type: string;
  template: string;
  variables: string[];
}

export class SMSService {
  
  // Send SMS message
  async sendSMS(data: SendSMSData) {
    try {
      if (!twilioClient) {
        console.log('Twi<PERSON> not configured, SMS would be sent:', data);
        return { success: true, messageId: 'mock-sms-id' };
      }

      // Format message with template if specified
      const formattedMessage = data.templateType 
        ? this.formatSMSTemplate(data.templateType, data.message, data.templateData)
        : data.message;

      // Send SMS via Twilio
      const message = await twilioClient.messages.create({
        body: formattedMessage,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: data.to,
      });

      // Create outbound communication record
      await communicationService.createCommunication({
        companyId: data.companyId,
        customerId: data.customerId,
        serviceTicketId: data.serviceTicketId,
        type: 'sms',
        direction: 'outbound',
        content: formattedMessage,
        fromPhone: process.env.TWILIO_PHONE_NUMBER,
        toPhone: data.to,
      });

      return {
        success: true,
        messageId: message.sid,
        status: message.status,
      };
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw new Error('Failed to send SMS');
    }
  }

  // Process inbound SMS
  async processInboundSMS(data: InboundSMSData) {
    try {
      console.log('Processing inbound SMS from:', data.from);

      // Find or create customer
      let customer = await customerService.findByPhone(data.from, data.companyId);
      
      if (!customer) {
        // Create new lead from SMS
        customer = await customerService.createLead({
          companyId: data.companyId,
          phone: data.from,
          source: 'sms',
          initialMessage: data.body,
        });
      }

      // Analyze SMS content
      const analysis = this.analyzeSMSContent(data.body);

      // Create communication record
      await communicationService.createCommunication({
        companyId: data.companyId,
        customerId: customer.id,
        type: 'sms',
        direction: 'inbound',
        content: data.body,
        fromPhone: data.from,
        toPhone: data.to,
      });

      // Send auto-response based on analysis
      await this.sendAutoResponse(data.from, customer, analysis, data.companyId);

      return {
        customer,
        analysis,
        processed: true,
      };
    } catch (error) {
      console.error('Error processing inbound SMS:', error);
      throw new Error('Failed to process inbound SMS');
    }
  }

  // Analyze SMS content
  private analyzeSMSContent(content: string) {
    const lowerContent = content.toLowerCase();
    
    // Detect intent
    let intent = 'general';
    let urgency = 'normal';
    let category = 'inquiry';

    if (lowerContent.includes('awaria') || lowerContent.includes('nie działa') || lowerContent.includes('pilne')) {
      intent = 'emergency';
      urgency = 'urgent';
      category = 'emergency';
    } else if (lowerContent.includes('serwis') || lowerContent.includes('przegląd') || lowerContent.includes('konserwacja')) {
      intent = 'maintenance';
      category = 'maintenance';
    } else if (lowerContent.includes('wycena') || lowerContent.includes('oferta') || lowerContent.includes('cennik')) {
      intent = 'quote';
      category = 'quote_request';
    } else if (lowerContent.includes('anuluj') || lowerContent.includes('przełóż') || lowerContent.includes('zmień')) {
      intent = 'reschedule';
      category = 'appointment_change';
    } else if (lowerContent.includes('tak') || lowerContent.includes('ok') || lowerContent.includes('potwierdzam')) {
      intent = 'confirmation';
      category = 'confirmation';
    } else if (lowerContent.includes('nie') || lowerContent.includes('anuluj')) {
      intent = 'cancellation';
      category = 'cancellation';
    }

    return {
      intent,
      urgency,
      category,
      requiresResponse: true,
      sentiment: this.detectSentiment(content),
    };
  }

  // Detect sentiment in SMS
  private detectSentiment(content: string): number {
    const lowerContent = content.toLowerCase();
    let sentiment = 0;

    const positiveWords = ['dziękuję', 'świetnie', 'ok', 'dobrze', 'tak'];
    const negativeWords = ['problem', 'źle', 'nie', 'awaria', 'zły'];

    positiveWords.forEach(word => {
      if (lowerContent.includes(word)) sentiment += 0.2;
    });

    negativeWords.forEach(word => {
      if (lowerContent.includes(word)) sentiment -= 0.3;
    });

    return Math.max(-1, Math.min(1, sentiment));
  }

  // Send auto-response based on analysis
  private async sendAutoResponse(to: string, customer: any, analysis: any, companyId: number) {
    let responseMessage = '';

    switch (analysis.intent) {
      case 'emergency':
        responseMessage = `🚨 ${customer.firstName}, otrzymaliśmy zgłoszenie awarii. Technik skontaktuje się w ciągu 2h. Awaryjny: ${process.env.EMERGENCY_PHONE}`;
        break;
      
      case 'maintenance':
        responseMessage = `🔧 ${customer.firstName}, dziękujemy za zgłoszenie serwisu. Skontaktujemy się w ciągu 24h w celu umówienia terminu.`;
        break;
      
      case 'quote':
        responseMessage = `💰 ${customer.firstName}, przygotujemy wycenę i prześlemy w ciągu 24h. Kontakt: ${process.env.COMPANY_PHONE}`;
        break;
      
      case 'confirmation':
        responseMessage = `✅ ${customer.firstName}, dziękujemy za potwierdzenie. Widzimy się w umówionym terminie!`;
        break;
      
      case 'cancellation':
        responseMessage = `❌ ${customer.firstName}, anulowanie zostało przyjęte. W razie potrzeby: ${process.env.COMPANY_PHONE}`;
        break;
      
      default:
        responseMessage = `📞 ${customer.firstName}, otrzymaliśmy Twoją wiadomość. Odpowiemy w ciągu 24h. ${process.env.COMPANY_NAME}`;
    }

    await this.sendSMS({
      to,
      message: responseMessage,
      companyId,
      customerId: customer.id,
      templateType: 'follow_up',
    });
  }

  // Send appointment reminder
  async sendAppointmentReminder(data: {
    customerId: number;
    companyId: number;
    appointmentDate: Date;
    technicianName: string;
    serviceType: string;
  }) {
    try {
      const customer = await customerService.getCustomerById(data.customerId, data.companyId);
      
      if (!customer.phone) {
        throw new Error('Customer has no phone number');
      }

      const appointmentTime = data.appointmentDate.toLocaleString('pl-PL', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });

      const message = `🔧 Przypomnienie: ${customer.firstName}, jutro ${appointmentTime} wizyta technika ${data.technicianName} w sprawie ${data.serviceType}. ${process.env.COMPANY_NAME}`;

      return await this.sendSMS({
        to: customer.phone,
        message,
        companyId: data.companyId,
        customerId: data.customerId,
        templateType: 'appointment_reminder',
      });
    } catch (error) {
      console.error('Error sending appointment reminder:', error);
      throw new Error('Failed to send appointment reminder');
    }
  }

  // Send service update
  async sendServiceUpdate(data: {
    customerId: number;
    companyId: number;
    serviceTicketId: number;
    status: string;
    message?: string;
  }) {
    try {
      const customer = await customerService.getCustomerById(data.customerId, data.companyId);
      
      if (!customer.phone) {
        throw new Error('Customer has no phone number');
      }

      let statusMessage = '';
      switch (data.status) {
        case 'assigned':
          statusMessage = '👨‍🔧 Technik został przydzielony do Twojego zgłoszenia';
          break;
        case 'in_progress':
          statusMessage = '🔧 Technik rozpoczął pracę na miejscu';
          break;
        case 'completed':
          statusMessage = '✅ Serwis został zakończony pomyślnie';
          break;
        case 'waiting_parts':
          statusMessage = '⏳ Oczekujemy na części zamienne';
          break;
        default:
          statusMessage = `📋 Status zgłoszenia: ${data.status}`;
      }

      const fullMessage = `${statusMessage}. ${data.message || ''} ${process.env.COMPANY_NAME}`;

      return await this.sendSMS({
        to: customer.phone,
        message: fullMessage,
        companyId: data.companyId,
        customerId: data.customerId,
        serviceTicketId: data.serviceTicketId,
        templateType: 'service_update',
      });
    } catch (error) {
      console.error('Error sending service update:', error);
      throw new Error('Failed to send service update');
    }
  }

  // Send quote ready notification
  async sendQuoteReady(data: {
    customerId: number;
    companyId: number;
    quoteAmount: number;
    validUntil: Date;
  }) {
    try {
      const customer = await customerService.getCustomerById(data.customerId, data.companyId);
      
      if (!customer.phone) {
        throw new Error('Customer has no phone number');
      }

      const amount = new Intl.NumberFormat('pl-PL', {
        style: 'currency',
        currency: 'PLN',
      }).format(data.quoteAmount);

      const validDate = data.validUntil.toLocaleDateString('pl-PL');

      const message = `💰 ${customer.firstName}, Twoja wycena jest gotowa: ${amount}. Ważna do ${validDate}. Kontakt: ${process.env.COMPANY_PHONE}`;

      return await this.sendSMS({
        to: customer.phone,
        message,
        companyId: data.companyId,
        customerId: data.customerId,
        templateType: 'quote_ready',
      });
    } catch (error) {
      console.error('Error sending quote ready notification:', error);
      throw new Error('Failed to send quote ready notification');
    }
  }

  // Format SMS template
  private formatSMSTemplate(templateType: string, message: string, data?: Record<string, any>): string {
    const templates: Record<string, string> = {
      appointment_reminder: `🔧 Przypomnienie: {{customerName}}, {{date}} wizyta technika {{technicianName}}. ${process.env.COMPANY_NAME}`,
      service_update: `📋 {{customerName}}, status zgłoszenia: {{status}}. {{message}} ${process.env.COMPANY_NAME}`,
      emergency_response: `🚨 {{customerName}}, otrzymaliśmy zgłoszenie awarii. Technik skontaktuje się w ciągu 2h. Awaryjny: ${process.env.EMERGENCY_PHONE}`,
      follow_up: `📞 {{customerName}}, otrzymaliśmy Twoją wiadomość. {{message}} ${process.env.COMPANY_NAME}`,
      quote_ready: `💰 {{customerName}}, Twoja wycena jest gotowa: {{amount}}. Ważna do {{validDate}}. ${process.env.COMPANY_NAME}`,
    };

    let template = templates[templateType] || message;

    // Replace variables if data provided
    if (data) {
      Object.keys(data).forEach(key => {
        template = template.replace(new RegExp(`{{${key}}}`, 'g'), data[key]);
      });
    }

    return template;
  }

  // Get SMS templates
  getSMSTemplates(): SMSTemplate[] {
    return [
      {
        type: 'appointment_reminder',
        template: '🔧 Przypomnienie: {{customerName}}, {{date}} wizyta technika {{technicianName}}.',
        variables: ['customerName', 'date', 'technicianName'],
      },
      {
        type: 'service_update',
        template: '📋 {{customerName}}, status zgłoszenia: {{status}}. {{message}}',
        variables: ['customerName', 'status', 'message'],
      },
      {
        type: 'emergency_response',
        template: '🚨 {{customerName}}, otrzymaliśmy zgłoszenie awarii. Technik skontaktuje się w ciągu 2h.',
        variables: ['customerName'],
      },
      {
        type: 'quote_ready',
        template: '💰 {{customerName}}, Twoja wycena jest gotowa: {{amount}}. Ważna do {{validDate}}.',
        variables: ['customerName', 'amount', 'validDate'],
      },
    ];
  }

  // Bulk SMS sending
  async sendBulkSMS(data: {
    customerIds: number[];
    companyId: number;
    message: string;
    templateType?: string;
    templateData?: Record<string, any>;
  }) {
    try {
      const results = [];
      
      for (const customerId of data.customerIds) {
        try {
          const customer = await customerService.getCustomerById(customerId, data.companyId);
          
          if (customer.phone) {
            const result = await this.sendSMS({
              to: customer.phone,
              message: data.message,
              companyId: data.companyId,
              customerId,
              templateType: data.templateType as any,
              templateData: { ...data.templateData, customerName: customer.firstName },
            });
            
            results.push({ customerId, success: true, messageId: result.messageId });
          } else {
            results.push({ customerId, success: false, error: 'No phone number' });
          }
        } catch (error) {
          results.push({ customerId, success: false, error: error.message });
        }
      }

      return {
        total: data.customerIds.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        results,
      };
    } catch (error) {
      console.error('Error sending bulk SMS:', error);
      throw new Error('Failed to send bulk SMS');
    }
  }
}

export const smsService = new SMSService();