'use client';

// 📞 Communication Hub - PEŁNA MOC WIATRU! ⚡
// Unified communication center with AI insights

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MessageSquare, 
  Users, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Filter,
  Search,
  Send,
  Eye,
  Star
} from 'lucide-react';

interface Communication {
  id: number;
  type: 'email' | 'phone' | 'sms' | 'in_person' | 'chat';
  direction: 'inbound' | 'outbound';
  subject?: string;
  content: string;
  fromEmail?: string;
  toEmail?: string;
  fromPhone?: string;
  toPhone?: string;
  customer?: {
    id: number;
    firstName: string;
    lastName: string;
  };
  sentimentScore?: number;
  category?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent' | 'emergency';
  isProcessed: boolean;
  requiresHumanAttention: boolean;
  createdAt: string;
}

const CommunicationHub: React.FC = () => {
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCommunication, setSelectedCommunication] = useState<Communication | null>(null);

  // Mock data for demonstration
  useEffect(() => {
    setTimeout(() => {
      setCommunications([
        {
          id: 1,
          type: 'email',
          direction: 'inbound',
          subject: 'Awaria klimatyzacji - pilne!',
          content: 'Dzień dobry, mam problem z klimatyzacją. Urządzenie nie chłodzi i wydaje dziwne dźwięki. Czy mogliby Państwo przyjechać dzisiaj?',
          fromEmail: '<EMAIL>',
          toEmail: '<EMAIL>',
          customer: { id: 1, firstName: 'Jan', lastName: 'Kowalski' },
          sentimentScore: -0.3,
          category: 'emergency',
          priority: 'urgent',
          isProcessed: false,
          requiresHumanAttention: true,
          createdAt: '2024-01-20T10:30:00Z',
        },
        {
          id: 2,
          type: 'sms',
          direction: 'inbound',
          content: 'Czy mogę umówić przegląd klimatyzacji na przyszły tydzień?',
          fromPhone: '+48 123 456 789',
          customer: { id: 2, firstName: 'Anna', lastName: 'Nowak' },
          sentimentScore: 0.2,
          category: 'maintenance',
          priority: 'normal',
          isProcessed: true,
          requiresHumanAttention: false,
          createdAt: '2024-01-20T09:15:00Z',
        },
        {
          id: 3,
          type: 'phone',
          direction: 'inbound',
          content: 'Rozmowa telefoniczna - prośba o wycenę instalacji klimatyzacji w biurze',
          fromPhone: '+48 987 654 321',
          customer: { id: 3, firstName: 'Piotr', lastName: 'Wiśniewski' },
          sentimentScore: 0.5,
          category: 'quote_request',
          priority: 'normal',
          isProcessed: true,
          requiresHumanAttention: false,
          createdAt: '2024-01-20T08:45:00Z',
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="w-4 h-4" />;
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'sms': return <MessageSquare className="w-4 h-4" />;
      default: return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'email': return 'bg-blue-100 text-blue-800';
      case 'phone': return 'bg-green-100 text-green-800';
      case 'sms': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'emergency': return 'bg-red-100 text-red-800 border-red-200';
      case 'urgent': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'high': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'normal': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSentimentIcon = (score?: number) => {
    if (!score) return null;
    if (score > 0.2) return <Star className="w-4 h-4 text-green-500" />;
    if (score < -0.2) return <AlertTriangle className="w-4 h-4 text-red-500" />;
    return <CheckCircle className="w-4 h-4 text-yellow-500" />;
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} min temu`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} godz. temu`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} dni temu`;
    }
  };

  const filteredCommunications = communications.filter(comm => {
    const matchesFilter = filter === 'all' || 
      (filter === 'unprocessed' && !comm.isProcessed) ||
      (filter === 'attention' && comm.requiresHumanAttention) ||
      (filter === comm.type);
    
    const matchesSearch = !searchTerm || 
      comm.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comm.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${comm.customer?.firstName} ${comm.customer?.lastName}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Centrum Komunikacji</h1>
            <p className="text-gray-600 mt-1">Zarządzanie wszystkimi komunikacjami z AI insights</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Nieprzetworzonych</p>
              <p className="text-2xl font-bold text-orange-600">
                {communications.filter(c => !c.isProcessed).length}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Wymaga uwagi</p>
              <p className="text-2xl font-bold text-red-600">
                {communications.filter(c => c.requiresHumanAttention).length}
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Email</p>
              <p className="text-2xl font-bold text-gray-900">
                {communications.filter(c => c.type === 'email').length}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Mail className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Telefon</p>
              <p className="text-2xl font-bold text-gray-900">
                {communications.filter(c => c.type === 'phone').length}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Phone className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">SMS</p>
              <p className="text-2xl font-bold text-gray-900">
                {communications.filter(c => c.type === 'sms').length}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <MessageSquare className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Średni Sentiment</p>
              <p className="text-2xl font-bold text-gray-900">
                {(communications.reduce((sum, c) => sum + (c.sentimentScore || 0), 0) / communications.length * 100).toFixed(0)}%
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Szukaj komunikacji..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
            >
              <option value="all">Wszystkie</option>
              <option value="unprocessed">Nieprzetworzonych</option>
              <option value="attention">Wymaga uwagi</option>
              <option value="email">Email</option>
              <option value="phone">Telefon</option>
              <option value="sms">SMS</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Communications List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-lg shadow-sm overflow-hidden"
      >
        <div className="divide-y divide-gray-200">
          {filteredCommunications.map((communication) => (
            <motion.div
              key={communication.id}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
              onClick={() => setSelectedCommunication(communication)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className={`p-2 rounded-full ${getTypeColor(communication.type)}`}>
                    {getTypeIcon(communication.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {communication.customer ? 
                          `${communication.customer.firstName} ${communication.customer.lastName}` : 
                          communication.fromEmail || communication.fromPhone
                        }
                      </h3>
                      {communication.sentimentScore && getSentimentIcon(communication.sentimentScore)}
                      {communication.priority && (
                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(communication.priority)}`}>
                          {communication.priority.toUpperCase()}
                        </span>
                      )}
                    </div>
                    
                    {communication.subject && (
                      <p className="text-sm font-medium text-gray-700 mb-1">
                        {communication.subject}
                      </p>
                    )}
                    
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {communication.content}
                    </p>
                    
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatTimeAgo(communication.createdAt)}
                      </span>
                      {communication.category && (
                        <span className="bg-gray-100 px-2 py-1 rounded">
                          {communication.category}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {!communication.isProcessed && (
                    <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                  )}
                  {communication.requiresHumanAttention && (
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                  )}
                  <Eye className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredCommunications.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Brak komunikacji</p>
              <p className="text-sm">Nie znaleziono komunikacji spełniających kryteria wyszukiwania.</p>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default CommunicationHub;