'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Award,
  BookOpen,
  Calendar,
  CheckCircle2,
  Clock,
  GraduationCap,
  MoreVertical,
  Plus,
  Target,
  TrendingUp,
  Users,
  XCircle,
} from 'lucide-react';

interface Training {
  id: string;
  title: string;
  type: 'technical' | 'safety' | 'soft-skills' | 'certification';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  startDate: string;
  endDate: string;
  instructor: string;
  location: string;
  participants: {
    id: string;
    name: string;
    role: string;
    status: 'registered' | 'attending' | 'completed' | 'failed';
  }[];
  materials: {
    id: string;
    name: string;
    type: string;
    url: string;
  }[];
}

interface Skill {
  id: string;
  name: string;
  category: 'technical' | 'safety' | 'soft-skills' | 'management';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  employees: {
    id: string;
    name: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    lastAssessed: string;
  }[];
  requiredTrainings: string[];
}

interface DevelopmentPlan {
  id: string;
  employeeId: string;
  employeeName: string;
  role: string;
  goals: {
    id: string;
    title: string;
    description: string;
    targetDate: string;
    status: 'not-started' | 'in-progress' | 'completed' | 'cancelled';
    skills: string[];
  }[];
  trainings: {
    id: string;
    title: string;
    status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
    completionDate?: string;
  }[];
  skills: {
    id: string;
    name: string;
    currentLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    targetLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    progress: number;
  }[];
}

interface TrainingDevelopmentProps {
  trainings: Training[];
  skills: Skill[];
  developmentPlans: DevelopmentPlan[];
}

const TrainingDevelopment: React.FC<TrainingDevelopmentProps> = ({
  trainings,
  skills,
  developmentPlans,
}) => {
  const [activeTab, setActiveTab] = useState('trainings');
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const tabs = [
    { id: 'trainings', label: 'Trainings', icon: GraduationCap },
    { id: 'skills', label: 'Skills', icon: Target },
    { id: 'development', label: 'Development', icon: TrendingUp },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-500';
      case 'in-progress':
        return 'text-blue-500';
      case 'scheduled':
        return 'text-yellow-500';
      case 'cancelled':
        return 'text-red-500';
      case 'failed':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'expert':
        return 'bg-purple-100 text-purple-800';
      case 'advanced':
        return 'bg-blue-100 text-blue-800';
      case 'intermediate':
        return 'bg-green-100 text-green-800';
      case 'beginner':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderTrainings = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Training Programs</h2>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-5 h-5" />
          <span>Schedule Training</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {trainings.map((training) => (
          <motion.div
            key={training.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6 cursor-pointer"
            onClick={() => setSelectedItem(training)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <GraduationCap className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold">{training.title}</h3>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Type</span>
                <span className="text-sm font-medium">{training.type}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <span
                  className={`text-sm font-medium ${getStatusColor(
                    training.status
                  )}`}
                >
                  {training.status}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Instructor</span>
                <span className="text-sm font-medium">{training.instructor}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Date</span>
                <span className="text-sm font-medium">
                  {training.startDate} - {training.endDate}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Participants</span>
                <span className="text-sm font-medium">
                  {training.participants.length}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderSkills = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Skill Matrix</h2>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-5 h-5" />
          <span>Add Skill</span>
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Skill
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Employees
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Required Trainings
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {skills.map((skill) => (
              <tr
                key={skill.id}
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => setSelectedItem(skill)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {skill.name}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                    {skill.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${getLevelColor(
                      skill.level
                    )}`}
                  >
                    {skill.level}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {skill.employees.length}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {skill.requiredTrainings.length}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderDevelopmentPlans = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Development Plans</h2>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-5 h-5" />
          <span>Create Plan</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {developmentPlans.map((plan) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6 cursor-pointer"
            onClick={() => setSelectedItem(plan)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Users className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold">{plan.employeeName}</h3>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Role</span>
                <span className="text-sm font-medium">{plan.role}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Goals</span>
                <span className="text-sm font-medium">{plan.goals.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Trainings</span>
                <span className="text-sm font-medium">
                  {plan.trainings.length}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Skills</span>
                <span className="text-sm font-medium">{plan.skills.length}</span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-4 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 ${
              activeTab === tab.id
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <tab.icon className="w-5 h-5" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'trainings' && renderTrainings()}
        {activeTab === 'skills' && renderSkills()}
        {activeTab === 'development' && renderDevelopmentPlans()}
      </div>

      {/* Details Modal */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg shadow-lg p-6 max-w-4xl w-full mx-4"
          >
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-xl font-semibold">{selectedItem.title || selectedItem.name}</h2>
                {selectedItem.description && (
                  <p className="text-sm text-gray-500">
                    {selectedItem.description}
                  </p>
                )}
              </div>
              <button
                onClick={() => setSelectedItem(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Training Details */}
              {selectedItem.type && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Details</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Type</span>
                        <span className="text-sm font-medium">
                          {selectedItem.type}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Status</span>
                        <span
                          className={`text-sm font-medium ${getStatusColor(
                            selectedItem.status
                          )}`}
                        >
                          {selectedItem.status}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Instructor</span>
                        <span className="text-sm font-medium">
                          {selectedItem.instructor}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Location</span>
                        <span className="text-sm font-medium">
                          {selectedItem.location}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Participants</h3>
                    <div className="space-y-2">
                      {selectedItem.participants?.map((participant: any) => (
                        <div
                          key={participant.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <div>
                            <p className="text-sm font-medium">{participant.name}</p>
                            <p className="text-xs text-gray-500">{participant.role}</p>
                          </div>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                              participant.status
                            )}`}
                          >
                            {participant.status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Skill Details */}
              {selectedItem.category && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Details</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Category</span>
                        <span className="text-sm font-medium">
                          {selectedItem.category}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Level</span>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${getLevelColor(
                            selectedItem.level
                          )}`}
                        >
                          {selectedItem.level}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Employees</h3>
                    <div className="space-y-2">
                      {selectedItem.employees?.map((employee: any) => (
                        <div
                          key={employee.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <div>
                            <p className="text-sm font-medium">{employee.name}</p>
                            <p className="text-xs text-gray-500">
                              Last assessed: {employee.lastAssessed}
                            </p>
                          </div>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${getLevelColor(
                              employee.level
                            )}`}
                          >
                            {employee.level}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Development Plan Details */}
              {selectedItem.goals && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Goals</h3>
                    <div className="space-y-2">
                      {selectedItem.goals.map((goal: any) => (
                        <div
                          key={goal.id}
                          className="p-4 bg-gray-50 rounded"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium">{goal.title}</h4>
                            <span
                              className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                                goal.status
                              )}`}
                            >
                              {goal.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500 mb-2">
                            {goal.description}
                          </p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>Target: {goal.targetDate}</span>
                            <span>{goal.skills.length} skills</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-2">Skills Progress</h3>
                    <div className="space-y-2">
                      {selectedItem.skills.map((skill: any) => (
                        <div
                          key={skill.id}
                          className="p-4 bg-gray-50 rounded"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium">{skill.name}</h4>
                            <div className="flex items-center space-x-2">
                              <span
                                className={`px-2 py-1 text-xs rounded-full ${getLevelColor(
                                  skill.currentLevel
                                )}`}
                              >
                                {skill.currentLevel}
                              </span>
                              <span className="text-gray-400">→</span>
                              <span
                                className={`px-2 py-1 text-xs rounded-full ${getLevelColor(
                                  skill.targetLevel
                                )}`}
                              >
                                {skill.targetLevel}
                              </span>
                            </div>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${skill.progress}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default TrainingDevelopment; 