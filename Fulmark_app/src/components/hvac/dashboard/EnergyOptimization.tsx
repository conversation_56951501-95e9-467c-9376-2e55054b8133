'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Zap,
  TrendingDown,
  DollarSign,
  Leaf,
  BarChart2,
  Clock,
  AlertCircle
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';

interface EnergyConsumption {
  timestamp: string;
  value: number;
  cost: number;
  type: 'heating' | 'cooling' | 'ventilation' | 'total';
}

interface OptimizationRecommendation {
  id: string;
  type: 'energy' | 'cost' | 'efficiency';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  potentialSavings: number;
  implementationCost: number;
  paybackPeriod: number;
}

interface EnergyMetrics {
  currentConsumption: number;
  averageConsumption: number;
  peakConsumption: number;
  costSavings: number;
  efficiencyScore: number;
  carbonReduction: number;
}

interface EnergyOptimizationProps {
  consumption: EnergyConsumption[];
  recommendations: OptimizationRecommendation[];
  metrics: EnergyMetrics;
}

const EnergyOptimization: React.FC<EnergyOptimizationProps> = ({
  consumption,
  recommendations,
  metrics
}) => {
  const getPriorityColor = (priority: OptimizationRecommendation['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Energy Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current Consumption</p>
              <p className="text-2xl font-semibold text-gray-900">{metrics.currentConsumption} kWh</p>
            </div>
            <Zap className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Cost Savings</p>
              <p className="text-2xl font-semibold text-gray-900">{formatCurrency(metrics.costSavings)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Efficiency Score</p>
              <p className="text-2xl font-semibold text-gray-900">{metrics.efficiencyScore}%</p>
            </div>
            <TrendingDown className="w-8 h-8 text-purple-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Carbon Reduction</p>
              <p className="text-2xl font-semibold text-gray-900">{metrics.carbonReduction} kg</p>
            </div>
            <Leaf className="w-8 h-8 text-emerald-500" />
          </div>
        </motion.div>
      </div>

      {/* Energy Consumption Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-6 rounded-lg shadow-sm"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Energy Consumption Trend</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={consumption}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Area
                yAxisId="left"
                type="monotone"
                dataKey="value"
                stroke="#3B82F6"
                fill="#93C5FD"
                name="Consumption (kWh)"
              />
              <Area
                yAxisId="right"
                type="monotone"
                dataKey="cost"
                stroke="#10B981"
                fill="#A7F3D0"
                name="Cost (PLN)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      {/* Optimization Recommendations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-lg shadow-sm"
      >
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Optimization Recommendations</h3>
          <div className="space-y-4">
            {recommendations.map((recommendation) => (
              <div key={recommendation.id} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <BarChart2 className="w-5 h-5 text-gray-500" />
                    <span className="font-medium text-gray-900">{recommendation.title}</span>
                  </div>
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityColor(recommendation.priority)}`}>
                    {recommendation.priority}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mb-2">{recommendation.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center text-sm text-gray-500">
                    <DollarSign className="w-4 h-4 mr-1" />
                    <span>Savings: {formatCurrency(recommendation.potentialSavings)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    <span>Cost: {formatCurrency(recommendation.implementationCost)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="w-4 h-4 mr-1" />
                    <span>Payback: {recommendation.paybackPeriod} months</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default EnergyOptimization; 