'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  TrendingUp,
  Star,
  MessageSquare,
  Clock,
  DollarSign,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as Re<PERSON>rtsBar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as Re<PERSON>rts<PERSON>ie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';

interface CustomerData {
  overview: {
    totalCustomers: number;
    activeCustomers: number;
    newCustomers: number;
    churnRate: number;
  };
  satisfaction: {
    averageRating: number;
    ratingDistribution: Array<{
      rating: number;
      count: number;
    }>;
    recentReviews: Array<{
      id: string;
      customer: string;
      rating: number;
      comment: string;
      date: string;
    }>;
  };
  journey: {
    stages: Array<{
      name: string;
      count: number;
      conversionRate: number;
    }>;
    recentActivities: Array<{
      id: string;
      customer: string;
      action: string;
      stage: string;
      date: string;
    }>;
  };
  revenue: {
    totalRevenue: number;
    averageRevenue: number;
    revenueByService: Array<{
      service: string;
      amount: number;
      percentage: number;
    }>;
    monthlyTrend: Array<{
      month: string;
      revenue: number;
      customers: number;
    }>;
  };
}

interface CustomerAnalyticsProps {
  data: CustomerData;
}

const CustomerAnalytics: React.FC<CustomerAnalyticsProps> = ({ data }) => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      {/* Customer Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Przegląd Klientów</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">Łączna Liczba</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.overview.totalCustomers}
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-green-500" />
              <span className="font-medium text-gray-900">Aktywni</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.overview.activeCustomers}
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">Nowi</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.overview.newCustomers}
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-red-500" />
              <span className="font-medium text-gray-900">Wskaźnik Rezygnacji</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.overview.churnRate}%
            </p>
          </div>
        </div>
      </motion.div>

      {/* Customer Satisfaction */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Satysfakcja Klientów</h3>
          <div className="flex items-center space-x-2">
            <Star className="w-5 h-5 text-yellow-500" />
            <span className="text-2xl font-bold text-gray-900">
              {data.satisfaction.averageRating.toFixed(1)}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={data.satisfaction.ratingDistribution}
                  dataKey="count"
                  nameKey="rating"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label
                >
                  {data.satisfaction.ratingDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-4">
            {data.satisfaction.recentReviews.map((review) => (
              <div key={review.id} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{review.customer}</span>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm text-gray-600">{review.rating}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{review.comment}</p>
                <p className="text-xs text-gray-500">
                  {new Date(review.date).toLocaleDateString('pl-PL')}
                </p>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Customer Journey */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Ścieżka Klienta</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            {data.journey.stages.map((stage, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{stage.name}</span>
                  <span className="text-sm text-gray-600">{stage.count} klientów</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${stage.conversionRate}%` }}
                  />
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Wskaźnik konwersji: {stage.conversionRate}%
                </p>
              </div>
            ))}
          </div>

          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 mb-2">Ostatnie Aktywności</h4>
            {data.journey.recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                <MessageSquare className="w-5 h-5 text-blue-500" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.customer}</p>
                  <p className="text-sm text-gray-600">{activity.action}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">{activity.stage}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(activity.date).toLocaleDateString('pl-PL')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Revenue Analytics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Analiza Przychodów</h3>
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5 text-green-500" />
            <span className="text-2xl font-bold text-gray-900">
              {data.revenue.totalRevenue.toLocaleString('pl-PL', {
                style: 'currency',
                currency: 'PLN'
              })}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart data={data.revenue.monthlyTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="revenue" fill="#0088FE" />
              </RechartsBarChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 mb-2">Przychody według Usług</h4>
            {data.revenue.revenueByService.map((service, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{service.service}</span>
                  <span className="text-sm text-gray-600">
                    {service.amount.toLocaleString('pl-PL', {
                      style: 'currency',
                      currency: 'PLN'
                    })}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${service.percentage}%` }}
                  />
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {service.percentage}% całkowitych przychodów
                </p>
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default CustomerAnalytics; 