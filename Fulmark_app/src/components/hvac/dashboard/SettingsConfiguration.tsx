'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Bell,
  Clock,
  Database,
  Globe,
  Lock,
  Mail,
  Moon,
  Palette,
  Shield,
  Sun,
  User,
  Zap,
} from 'lucide-react';

interface NotificationSetting {
  id: string;
  type: 'email' | 'push' | 'sms';
  name: string;
  description: string;
  enabled: boolean;
}

interface SecuritySetting {
  id: string;
  name: string;
  description: string;
  value: string;
  type: 'password' | '2fa' | 'session' | 'api';
}

interface PreferenceSetting {
  id: string;
  name: string;
  description: string;
  value: string | boolean;
  type: 'theme' | 'language' | 'timezone' | 'dateFormat';
}

interface SettingsConfigurationProps {
  notifications: NotificationSetting[];
  security: SecuritySetting[];
  preferences: PreferenceSetting[];
}

const SettingsConfiguration: React.FC<SettingsConfigurationProps> = ({
  notifications,
  security,
  preferences,
}) => {
  const [activeTab, setActiveTab] = useState('notifications');

  const tabs = [
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'preferences', label: 'Preferences', icon: Palette },
  ];

  const renderNotifications = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {notifications.map((setting) => (
          <motion.div
            key={setting.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{setting.name}</h3>
                <p className="text-sm text-gray-500">{setting.description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={setting.enabled}
                  onChange={() => {}}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderSecurity = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {security.map((setting) => (
          <motion.div
            key={setting.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6"
          >
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">{setting.name}</h3>
                <p className="text-sm text-gray-500">{setting.description}</p>
              </div>
              {setting.type === 'password' && (
                <div className="flex items-center space-x-2">
                  <input
                    type="password"
                    value={setting.value}
                    className="flex-1 px-3 py-2 border rounded-md"
                    placeholder="Enter new password"
                  />
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Update
                  </button>
                </div>
              )}
              {setting.type === '2fa' && (
                <div className="flex items-center space-x-2">
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Enable 2FA
                  </button>
                </div>
              )}
              {setting.type === 'session' && (
                <div className="flex items-center space-x-2">
                  <button className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                    End All Sessions
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderPreferences = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {preferences.map((setting) => (
          <motion.div
            key={setting.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow p-6"
          >
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">{setting.name}</h3>
                <p className="text-sm text-gray-500">{setting.description}</p>
              </div>
              {setting.type === 'theme' && (
                <div className="flex items-center space-x-4">
                  <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                    <Sun className="w-5 h-5" />
                  </button>
                  <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                    <Moon className="w-5 h-5" />
                  </button>
                </div>
              )}
              {setting.type === 'language' && (
                <select className="w-full px-3 py-2 border rounded-md">
                  <option value="en">English</option>
                  <option value="pl">Polski</option>
                  <option value="de">Deutsch</option>
                </select>
              )}
              {setting.type === 'timezone' && (
                <select className="w-full px-3 py-2 border rounded-md">
                  <option value="UTC">UTC</option>
                  <option value="CET">Central European Time</option>
                  <option value="EET">Eastern European Time</option>
                </select>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-4 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 ${
              activeTab === tab.id
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <tab.icon className="w-5 h-5" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'notifications' && renderNotifications()}
        {activeTab === 'security' && renderSecurity()}
        {activeTab === 'preferences' && renderPreferences()}
      </div>
    </div>
  );
};

export default SettingsConfiguration; 