'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Chart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  FileText,
  Download,
  Share2,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Settings,
  AlertCircle,
} from 'lucide-react';

interface ReportData {
  id: string;
  title: string;
  type: 'financial' | 'operational' | 'customer' | 'maintenance';
  period: string;
  status: 'generated' | 'pending' | 'failed';
  generatedAt: string;
  generatedBy: string;
  metrics: {
    value: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  };
}

interface AnalyticsData {
  financialMetrics: {
    revenue: number;
    expenses: number;
    profit: number;
    profitMargin: number;
    monthlyTrend: Array<{
      month: string;
      revenue: number;
      expenses: number;
      profit: number;
    }>;
  };
  operationalMetrics: {
    efficiency: number;
    uptime: number;
    responseTime: number;
    monthlyPerformance: Array<{
      month: string;
      efficiency: number;
      uptime: number;
      responseTime: number;
    }>;
  };
  customerMetrics: {
    satisfaction: number;
    retention: number;
    acquisition: number;
    customerSegments: Array<{
      segment: string;
      value: number;
    }>;
  };
  maintenanceMetrics: {
    preventiveMaintenance: number;
    correctiveMaintenance: number;
    maintenanceCost: number;
    monthlyMaintenance: Array<{
      month: string;
      preventive: number;
      corrective: number;
      cost: number;
    }>;
  };
}

interface ReportsAnalyticsProps {
  reports: ReportData[];
  analytics: AnalyticsData;
}

const ReportsAnalytics: React.FC<ReportsAnalyticsProps> = ({
  reports,
  analytics,
}) => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN',
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      {/* Financial Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Revenue</p>
              <p className="text-2xl font-bold">
                {formatCurrency(analytics.financialMetrics.revenue)}
              </p>
              <p className="text-sm text-green-500">
                +{formatPercentage(analytics.financialMetrics.revenue * 0.1)}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Profit Margin</p>
              <p className="text-2xl font-bold">
                {formatPercentage(analytics.financialMetrics.profitMargin)}
              </p>
              <p className="text-sm text-green-500">
                +{formatPercentage(analytics.financialMetrics.profitMargin * 0.05)}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Customer Satisfaction</p>
              <p className="text-2xl font-bold">
                {formatPercentage(analytics.customerMetrics.satisfaction)}
              </p>
              <p className="text-sm text-green-500">
                +{formatPercentage(analytics.customerMetrics.satisfaction * 0.02)}
              </p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Efficiency</p>
              <p className="text-2xl font-bold">
                {formatPercentage(analytics.operationalMetrics.efficiency)}
              </p>
              <p className="text-sm text-green-500">
                +{formatPercentage(analytics.operationalMetrics.efficiency * 0.03)}
              </p>
            </div>
            <Settings className="w-8 h-8 text-purple-500" />
          </div>
        </motion.div>
      </div>

      {/* Financial Trends */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-lg shadow p-6"
      >
        <h2 className="text-lg font-semibold mb-4">Financial Trends</h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={analytics.financialMetrics.monthlyTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#0088FE"
                name="Revenue"
              />
              <Line
                type="monotone"
                dataKey="expenses"
                stroke="#FF8042"
                name="Expenses"
              />
              <Line
                type="monotone"
                dataKey="profit"
                stroke="#00C49F"
                name="Profit"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      {/* Customer Segments */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-lg shadow p-6"
      >
        <h2 className="text-lg font-semibold mb-4">Customer Segments</h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={analytics.customerMetrics.customerSegments}
                dataKey="value"
                nameKey="segment"
                cx="50%"
                cy="50%"
                outerRadius={100}
                label
              >
                {analytics.customerMetrics.customerSegments.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      {/* Maintenance Performance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-lg shadow p-6"
      >
        <h2 className="text-lg font-semibold mb-4">Maintenance Performance</h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analytics.maintenanceMetrics.monthlyMaintenance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="preventive"
                fill="#0088FE"
                name="Preventive"
              />
              <Bar
                dataKey="corrective"
                fill="#FF8042"
                name="Corrective"
              />
              <Bar
                dataKey="cost"
                fill="#00C49F"
                name="Cost"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      {/* Recent Reports */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white rounded-lg shadow p-6"
      >
        <h2 className="text-lg font-semibold mb-4">Recent Reports</h2>
        <div className="space-y-4">
          {reports.map((report) => (
            <div
              key={report.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-4">
                <FileText className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium">{report.title}</p>
                  <p className="text-sm text-gray-500">
                    {report.type} • {report.period}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    report.status === 'generated'
                      ? 'bg-green-100 text-green-800'
                      : report.status === 'pending'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {report.status}
                </span>
                <button className="p-1 text-gray-500 hover:text-gray-700">
                  <Download className="w-5 h-5" />
                </button>
                <button className="p-1 text-gray-500 hover:text-gray-700">
                  <Share2 className="w-5 h-5" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default ReportsAnalytics; 