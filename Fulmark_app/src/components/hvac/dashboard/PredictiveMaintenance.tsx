'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle2,
  Clock,
  Gauge,
  HardDrive,
  Settings,
  Thermometer,
  Wind
} from 'lucide-react';

interface MaintenanceData {
  systemHealth: {
    overall: number;
    components: Array<{
      name: string;
      health: number;
      status: 'optimal' | 'warning' | 'critical';
      lastMaintenance: string;
      nextMaintenance: string;
    }>;
  };
  predictiveAlerts: Array<{
    id: string;
    component: string;
    type: 'maintenance' | 'replacement' | 'inspection';
    severity: 'low' | 'medium' | 'high';
    predictedDate: string;
    description: string;
    impact: string;
  }>;
  maintenanceHistory: Array<{
    id: string;
    date: string;
    type: string;
    description: string;
    technician: string;
    cost: number;
    status: 'completed' | 'scheduled' | 'in-progress';
  }>;
  efficiencyMetrics: {
    energyEfficiency: number;
    operationalEfficiency: number;
    maintenanceEfficiency: number;
    costSavings: number;
  };
}

interface PredictiveMaintenanceProps {
  data: MaintenanceData;
}

const PredictiveMaintenance: React.FC<PredictiveMaintenanceProps> = ({ data }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-500';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-500';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-500';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-5 h-5 text-green-500" />;
      case 'scheduled':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'in-progress':
        return <Settings className="w-5 h-5 text-yellow-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* System Health Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Stan Systemu</h3>
          <div className="flex items-center space-x-2">
            <Gauge className="w-5 h-5 text-blue-500" />
            <span className="text-2xl font-bold text-gray-900">
              {data.systemHealth.overall}%
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.systemHealth.components.map((component, index) => (
            <div
              key={index}
              className="p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">{component.name}</span>
                <span className={`text-sm ${getStatusColor(component.status)}`}>
                  {component.status === 'optimal' ? 'Optymalny' :
                   component.status === 'warning' ? 'Ostrzeżenie' : 'Krytyczny'}
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Ostatnia konserwacja:</span>
                  <span>{new Date(component.lastMaintenance).toLocaleDateString('pl-PL')}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Następna konserwacja:</span>
                  <span>{new Date(component.nextMaintenance).toLocaleDateString('pl-PL')}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Predictive Alerts */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Alerty Predykcyjne</h3>
        <div className="space-y-4">
          {data.predictiveAlerts.map((alert) => (
            <div
              key={alert.id}
              className={`p-4 border-l-4 ${getSeverityColor(alert.severity)} rounded-r-lg`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5" />
                  <span className="font-medium">{alert.component}</span>
                </div>
                <span className="text-sm">
                  {new Date(alert.predictedDate).toLocaleDateString('pl-PL')}
                </span>
              </div>
              <p className="text-sm mb-2">{alert.description}</p>
              <p className="text-sm font-medium">Wpływ: {alert.impact}</p>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Maintenance History */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Historia Konserwacji</h3>
        <div className="space-y-4">
          {data.maintenanceHistory.map((maintenance) => (
            <div
              key={maintenance.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-4">
                {getStatusIcon(maintenance.status)}
                <div>
                  <p className="font-medium text-gray-900">{maintenance.type}</p>
                  <p className="text-sm text-gray-600">{maintenance.description}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">
                  {new Date(maintenance.date).toLocaleDateString('pl-PL')}
                </p>
                <p className="text-sm font-medium text-gray-900">
                  {maintenance.cost.toLocaleString('pl-PL', {
                    style: 'currency',
                    currency: 'PLN'
                  })}
                </p>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Efficiency Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Metryki Efektywności</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Thermometer className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">Efektywność Energetyczna</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.efficiencyMetrics.energyEfficiency}%
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Wind className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">Efektywność Operacyjna</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.efficiencyMetrics.operationalEfficiency}%
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Settings className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">Efektywność Konserwacji</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.efficiencyMetrics.maintenanceEfficiency}%
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <HardDrive className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">Oszczędności Kosztów</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.efficiencyMetrics.costSavings.toLocaleString('pl-PL', {
                style: 'currency',
                currency: 'PLN'
              })}
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default PredictiveMaintenance; 