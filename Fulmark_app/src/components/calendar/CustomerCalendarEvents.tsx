'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Loader2, Calendar, Clock, MapPin, Users, Brain, TrendingUp, TrendingDown } from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface CalendarEvent {
  id: string;
  subject: string;
  bodyContent: string | null;
  startTime: string | null;
  endTime: string | null;
  location: string | null;
  attendees: any[];
  organizer: any;
  isAllDay: boolean;
  isCancelled: boolean;
  importance: 'low' | 'normal' | 'high';
  eventType: string | null;
  priority: number;
  confidenceScore: string;
  sentimentScore: string | null;
  aiAnalysis: any;
  actionItems: string[];
  createdAt: string;
}

interface CalendarInsights {
  totalMeetings: number;
  lastMeetingDate: string | null;
  nextMeetingDate: string | null;
  meetingFrequencyDays: string;
  preferredMeetingTimes: Record<string, number>;
  commonMeetingTypes: string[];
  engagementScore: string;
  lastCalculatedAt: string;
}

interface CustomerCalendarData {
  events: CalendarEvent[];
  insights: CalendarInsights | null;
  statistics: {
    totalEvents: number;
    futureEvents: number;
    recentEvents: number;
    eventsByType: Record<string, number>;
    averageSentiment: number;
    lastEventDate: string | null;
    nextEventDate: string | null;
  };
}

interface CustomerCalendarEventsProps {
  customerId: string;
}

export function CustomerCalendarEvents({ customerId }: CustomerCalendarEventsProps) {
  const [calendarData, setCalendarData] = useState<CustomerCalendarData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdatingInsights, setIsUpdatingInsights] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchCalendarData();
  }, [customerId]);

  const fetchCalendarData = async () => {
    try {
      const response = await fetch(`/api/customers/${customerId}/calendar?includeInsights=true`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch calendar data');
      }

      const data = await response.json();
      setCalendarData(data);
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendar events',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateInsights = async () => {
    setIsUpdatingInsights(true);
    try {
      const response = await fetch(`/api/customers/${customerId}/calendar`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to update insights');
      }

      const data = await response.json();
      
      toast({
        title: 'Insights Updated',
        description: 'Customer calendar insights have been recalculated',
      });

      // Refresh data
      await fetchCalendarData();
    } catch (error) {
      console.error('Error updating insights:', error);
      toast({
        title: 'Update Failed',
        description: 'Failed to update calendar insights',
        variant: 'destructive',
      });
    } finally {
      setIsUpdatingInsights(false);
    }
  };

  const getEventTypeBadge = (eventType: string | null) => {
    const type = eventType || 'other';
    const colors = {
      sales_meeting: 'bg-green-100 text-green-800',
      support_call: 'bg-blue-100 text-blue-800',
      installation: 'bg-purple-100 text-purple-800',
      consultation: 'bg-orange-100 text-orange-800',
      follow_up: 'bg-yellow-100 text-yellow-800',
      maintenance: 'bg-red-100 text-red-800',
      training: 'bg-indigo-100 text-indigo-800',
      other: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge className={colors[type as keyof typeof colors] || colors.other}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getSentimentIcon = (sentimentScore: string | null) => {
    if (!sentimentScore) return null;
    const score = parseFloat(sentimentScore);
    
    if (score > 0.2) {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else if (score < -0.2) {
      return <TrendingDown className="w-4 h-4 text-red-600" />;
    }
    return <div className="w-4 h-4 rounded-full bg-gray-400" />;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const formatTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading calendar events...
        </CardContent>
      </Card>
    );
  }

  if (!calendarData) {
    return (
      <Card>
        <CardContent className="text-center p-6">
          <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600">No calendar data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Calendar Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Calendar Overview
            </div>
            <Button
              onClick={updateInsights}
              disabled={isUpdatingInsights}
              variant="outline"
              size="sm"
            >
              {isUpdatingInsights ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Brain className="w-4 h-4 mr-2" />
                  Update Insights
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{calendarData.statistics.totalEvents}</div>
              <div className="text-sm text-gray-600">Total Events</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{calendarData.statistics.futureEvents}</div>
              <div className="text-sm text-gray-600">Upcoming</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{calendarData.statistics.recentEvents}</div>
              <div className="text-sm text-gray-600">Recent (30d)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {calendarData.statistics.averageSentiment > 0 ? '+' : ''}{calendarData.statistics.averageSentiment}
              </div>
              <div className="text-sm text-gray-600">Avg Sentiment</div>
            </div>
          </div>

          {calendarData.insights && (
            <div className="mt-6 pt-6 border-t">
              <h4 className="font-medium mb-3">AI Insights</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Meeting Frequency:</span>
                  <div className="font-medium">
                    Every {Math.round(parseFloat(calendarData.insights.meetingFrequencyDays))} days
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Engagement Score:</span>
                  <div className="font-medium">
                    {Math.round(parseFloat(calendarData.insights.engagementScore) * 100)}%
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Common Types:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {calendarData.insights.commonMeetingTypes.slice(0, 3).map((type) => (
                      <Badge key={type} variant="outline" className="text-xs">
                        {type.replace('_', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Calendar Events List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Calendar Events</CardTitle>
          <CardDescription>
            AI-analyzed calendar events with customer insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          {calendarData.events.length === 0 ? (
            <div className="text-center py-6">
              <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">No calendar events found for this customer</p>
            </div>
          ) : (
            <div className="space-y-4">
              {calendarData.events.map((event) => (
                <div key={event.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium">{event.subject || 'Untitled Event'}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {formatDate(event.startTime)}
                        </div>
                        {event.location && (
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {event.location}
                          </div>
                        )}
                        {event.attendees.length > 0 && (
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            {event.attendees.length} attendees
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getSentimentIcon(event.sentimentScore)}
                      {getEventTypeBadge(event.eventType)}
                    </div>
                  </div>

                  {event.bodyContent && (
                    <div className="text-sm text-gray-700 bg-gray-50 rounded p-3">
                      {event.bodyContent.substring(0, 200)}
                      {event.bodyContent.length > 200 && '...'}
                    </div>
                  )}

                  {event.actionItems.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Action Items:</span>
                      <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                        {event.actionItems.slice(0, 3).map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div>
                      Confidence: {Math.round(parseFloat(event.confidenceScore) * 100)}%
                    </div>
                    <div>
                      Priority: {event.priority}/5
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
