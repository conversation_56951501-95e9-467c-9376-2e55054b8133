// 👨‍🔧 Technicians API - PEŁNA MOC WIATRU! ⚡

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { technicianService } from '@/services/technician.service';

const createTechnicianSchema = z.object({
  companyId: z.number().positive(),
  employeeId: z.string().max(50).optional(),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  email: z.string().email().optional(),
  phone: z.string().max(50).optional(),
  specializations: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  hourlyRate: z.number().positive().optional(),
  workingHours: z.object({
    monday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
    tuesday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
    wednesday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
    thursday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
    friday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
    saturday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
    sunday: z.object({ start: z.string(), end: z.string(), available: z.boolean() }),
  }).optional(),
  hireDate: z.string().datetime().optional(),
});

const filtersSchema = z.object({
  companyId: z.number().positive(),
  status: z.array(z.enum(['available', 'busy', 'on_route', 'offline', 'break'])).optional(),
  specialization: z.string().optional(),
  isActive: z.boolean().optional(),
  search: z.string().optional(),
  availableNow: z.boolean().optional(),
  nearLatitude: z.number().optional(),
  nearLongitude: z.number().optional(),
  radiusKm: z.number().positive().optional(),
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
});

// GET /api/technicians - Get technicians with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const queryParams = {
      companyId: Number(searchParams.get('companyId')),
      status: searchParams.get('status')?.split(',') as any,
      specialization: searchParams.get('specialization') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      availableNow: searchParams.get('availableNow') ? searchParams.get('availableNow') === 'true' : undefined,
      nearLatitude: searchParams.get('nearLatitude') ? Number(searchParams.get('nearLatitude')) : undefined,
      nearLongitude: searchParams.get('nearLongitude') ? Number(searchParams.get('nearLongitude')) : undefined,
      radiusKm: searchParams.get('radiusKm') ? Number(searchParams.get('radiusKm')) : undefined,
      page: searchParams.get('page') ? Number(searchParams.get('page')) : 1,
      limit: searchParams.get('limit') ? Number(searchParams.get('limit')) : 20,
    };

    const validatedParams = filtersSchema.parse(queryParams);
    const { page, limit, nearLatitude, nearLongitude, radiusKm, ...filters } = validatedParams;
    
    // Add location filter if coordinates provided
    const processedFilters = {
      ...filters,
      nearLocation: (nearLatitude && nearLongitude && radiusKm) ? {
        latitude: nearLatitude,
        longitude: nearLongitude,
        radiusKm,
      } : undefined,
    };
    
    const result = await technicianService.getTechnicians(processedFilters, page, limit);
    
    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('GET /api/technicians error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch technicians' },
      { status: 500 }
    );
  }
}

// POST /api/technicians - Create new technician
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createTechnicianSchema.parse(body);
    
    // Convert date strings to Date objects
    const processedData = {
      ...validatedData,
      hireDate: validatedData.hireDate ? new Date(validatedData.hireDate) : undefined,
    };
    
    const technician = await technicianService.createTechnician(processedData);
    
    return NextResponse.json({
      success: true,
      data: technician,
    }, { status: 201 });
  } catch (error) {
    console.error('POST /api/technicians error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to create technician' },
      { status: 500 }
    );
  }
}