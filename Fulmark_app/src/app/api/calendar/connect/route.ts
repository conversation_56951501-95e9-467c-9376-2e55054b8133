import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { microsoftGraphService } from '../../../../services/microsoft-graph';

/**
 * Initiate Microsoft Graph OAuth flow for calendar access
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Generate state parameter for security
    const state = JSON.stringify({
      userId,
      timestamp: Date.now(),
      nonce: Math.random().toString(36).substring(2),
    });

    // Get authorization URL
    const authUrl = await microsoftGraphService.getAuthUrl(
      Buffer.from(state).toString('base64')
    );

    return NextResponse.json({
      authUrl,
      message: 'Redirect to this URL to authorize calendar access',
    });

  } catch (error) {
    console.error('Calendar connect error:', error);
    return NextResponse.json(
      { error: 'Failed to initiate calendar connection' },
      { status: 500 }
    );
  }
}

/**
 * Get current calendar connection status
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Import db here to avoid circular dependencies
    const { db } = await import('../../../../libs/db');
    const { calendarConnections } = await import('../../../../models/calendar');
    const { eq } = await import('drizzle-orm');

    // Get user's calendar connections
    const connections = await db
      .select({
        id: calendarConnections.id,
        isActive: calendarConnections.isActive,
        lastSyncAt: calendarConnections.lastSyncAt,
        syncStatus: calendarConnections.syncStatus,
        errorMessage: calendarConnections.errorMessage,
        createdAt: calendarConnections.createdAt,
        scopes: calendarConnections.scopes,
      })
      .from(calendarConnections)
      .where(eq(calendarConnections.userId, userId));

    return NextResponse.json({
      connections,
      hasActiveConnection: connections.some(conn => conn.isActive),
    });

  } catch (error) {
    console.error('Calendar status error:', error);
    return NextResponse.json(
      { error: 'Failed to get calendar connection status' },
      { status: 500 }
    );
  }
}
