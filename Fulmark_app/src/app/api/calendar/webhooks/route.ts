import { NextRequest, NextResponse } from 'next/server';
import { microsoftGraphService, type WebhookNotification } from '../../../../services/microsoft-graph';
import { calendarSyncService } from '../../../../services/calendar-sync';
import { db } from '../../../../libs/db';
import { calendarConnections, calendarSyncLogs } from '../../../../models/calendar';
import { eq } from 'drizzle-orm';

/**
 * Handle Microsoft Graph webhook notifications for calendar changes
 */
export async function POST(request: NextRequest) {
  try {
    // Get validation token for subscription validation
    const searchParams = request.nextUrl.searchParams;
    const validationToken = searchParams.get('validationToken');

    // Handle subscription validation
    if (validationToken) {
      console.log('Validating webhook subscription');
      return new NextResponse(validationToken, {
        status: 200,
        headers: { 'Content-Type': 'text/plain' },
      });
    }

    // Parse webhook notification
    const notifications: { value: WebhookNotification[] } = await request.json();

    if (!notifications.value || !Array.isArray(notifications.value)) {
      return NextResponse.json(
        { error: 'Invalid notification format' },
        { status: 400 }
      );
    }

    console.log(`Received ${notifications.value.length} calendar webhook notifications`);

    // Process each notification
    for (const notification of notifications.value) {
      try {
        await processWebhookNotification(notification);
      } catch (error) {
        console.error('Error processing webhook notification:', error);
        // Continue processing other notifications
      }
    }

    return NextResponse.json({ message: 'Webhook processed successfully' });

  } catch (error) {
    console.error('Calendar webhook error:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}

/**
 * Process individual webhook notification
 */
async function processWebhookNotification(notification: WebhookNotification) {
  // Validate notification
  if (!microsoftGraphService.validateWebhookNotification(undefined, notification.clientState)) {
    console.error('Invalid webhook notification client state');
    return;
  }

  console.log(`Processing webhook notification: ${notification.changeType} for resource ${notification.resource}`);

  // Extract subscription ID to find the connection
  const subscriptionId = notification.subscriptionId;

  // Find the calendar connection associated with this subscription
  // Note: In a real implementation, you would store subscription IDs in the database
  // For now, we'll trigger sync for all active connections
  const activeConnections = await db
    .select()
    .from(calendarConnections)
    .where(eq(calendarConnections.isActive, true));

  if (!activeConnections.length) {
    console.log('No active calendar connections found for webhook');
    return;
  }

  // Log webhook sync
  const syncPromises = activeConnections.map(async (connection) => {
    try {
      // Create sync log for webhook
      await db.insert(calendarSyncLogs).values({
        connectionId: connection.id,
        syncType: 'webhook',
        status: 'started',
        eventsProcessed: 0,
        eventsCreated: 0,
        eventsUpdated: 0,
        eventsDeleted: 0,
      });

      // Trigger incremental sync for this connection
      await calendarSyncService.syncCalendarEvents(connection.id, 'incremental');

      console.log(`Webhook sync completed for connection ${connection.id}`);
    } catch (error) {
      console.error(`Webhook sync failed for connection ${connection.id}:`, error);
    }
  });

  // Execute syncs in parallel
  await Promise.all(syncPromises);
}

/**
 * Handle webhook subscription management
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const action = searchParams.get('action');

    if (action === 'health') {
      return NextResponse.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        message: 'Calendar webhook endpoint is operational',
      });
    }

    return NextResponse.json({
      message: 'Calendar webhook endpoint',
      supportedMethods: ['POST'],
      description: 'Receives Microsoft Graph calendar change notifications',
    });

  } catch (error) {
    console.error('Calendar webhook GET error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

/**
 * Handle webhook subscription renewal
 */
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { subscriptionId, expirationDateTime } = body;

    if (!subscriptionId || !expirationDateTime) {
      return NextResponse.json(
        { error: 'Subscription ID and expiration date are required' },
        { status: 400 }
      );
    }

    // In a real implementation, you would:
    // 1. Find the connection associated with this subscription
    // 2. Get the access token for that connection
    // 3. Renew the subscription using Microsoft Graph API

    console.log(`Webhook subscription renewal requested for ${subscriptionId}`);

    return NextResponse.json({
      message: 'Subscription renewal initiated',
      subscriptionId,
      expirationDateTime,
    });

  } catch (error) {
    console.error('Calendar webhook renewal error:', error);
    return NextResponse.json(
      { error: 'Failed to renew subscription' },
      { status: 500 }
    );
  }
}
