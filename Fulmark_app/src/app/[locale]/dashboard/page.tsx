import { getTranslations } from 'next-intl/server';
import { auth } from '@clerk/nextjs';
import { redirect } from 'next/navigation';
import HVACDashboard from '@/components/hvac/dashboard/HVACDashboard';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Dashboard',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const DashboardPage = async () => {
  const { userId } = auth();
  
  if (!userId) {
    redirect('/sign-in');
  }

  return <HVACDashboard />;
};

export default DashboardPage;